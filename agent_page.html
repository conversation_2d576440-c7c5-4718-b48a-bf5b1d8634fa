<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Панель риелтора - RealEstate CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a4a14 0%, #2d5a27 25%, #4a7c59 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            color: #e8f5e8;
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #e8f5e8;
        }

        .exit-btn {
            background: none;
            border: none;
            color: #a8d5a8;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .exit-btn:hover {
            background: rgba(168, 213, 168, 0.2);
            transform: rotate(15deg);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 2rem;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4caf50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }

        .agent-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 0.5rem;
        }

        .agent-region {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .agent-contacts {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #4caf50;
            color: #4caf50;
        }

        .btn-outline:hover {
            background: #4caf50;
            color: white;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .personal-link {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .link-title {
            font-size: 0.9rem;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 0.5rem;
        }

        .link-url {
            background: #f5f5f5;
            padding: 0.5rem;
            border-radius: 8px;
            font-size: 0.8rem;
            color: #666;
            word-break: break-all;
            margin-bottom: 0.5rem;
        }

        .map-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            flex: 1;
        }

        .map-placeholder {
            background: #e8f5e8;
            border-radius: 10px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2d5a27;
            font-size: 1.2rem;
            border: 2px dashed #4caf50;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .property-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .property-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-color: #4caf50;
        }

        .property-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 0.5rem;
        }

        .property-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #4caf50;
            margin-bottom: 0.5rem;
        }

        .property-type {
            background: #e8f5e8;
            color: #2d5a27;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        .property-address {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .property-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: space-between;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 2rem;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d5a27;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #333;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
        }

        .form-row .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2d5a27;
            font-weight: 500;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4caf50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .file-upload {
            border: 2px dashed #4caf50;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f9f9f9;
        }

        .file-upload:hover {
            background: #f0f8f0;
            border-color: #45a049;
        }

        .file-upload.drag-over {
            background: #e8f5e8;
            border-color: #2d5a27;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .payment-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #856404;
        }

        .payment-notice h4 {
            margin-bottom: 0.5rem;
            color: #533f03;
        }

        .calendar-container {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-day {
            background: white;
            padding: 0.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .calendar-day:hover {
            background: #e8f5e8;
        }

        .calendar-day.selected {
            background: #4caf50;
            color: white;
        }

        .calendar-day.booked {
            background: #ffcdd2;
            color: #d32f2f;
        }

        .calendar-day.unavailable {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
        }

        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .sidebar {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }

            .properties-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                grid-template-columns: 1fr;
            }

            .form-row {
                flex-direction: column;
            }

            .property-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">
            🏠 RealEstate CRM
        </div>
        <div class="user-info">
            <span>Панель риелтора</span>
            <button class="exit-btn" onclick="logout()" title="Выйти">🚪</button>
        </div>
    </header>

    <div class="dashboard">
        <div class="sidebar">
            <!-- Карточка агента -->
            <div class="agent-card">
                <div class="avatar">👤</div>
                <div class="agent-name">Иван Петров</div>
                <div class="agent-region">Москва</div>
                <div class="agent-contacts">
                    <div class="contact-item">
                        <span>📧</span>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span>📱</span>
                        <span>+7 (999) 123-45-67</span>
                    </div>
                </div>
                <button class="btn btn-outline btn-small" onclick="editProfile()">
                    ✏️ Редактировать
                </button>
            </div>

            <!-- Персональная ссылка -->
            <div class="personal-link">
                <div class="link-title">Персональная ссылка</div>
                <div class="link-url">https://realestate.com/agent/ivan-petrov</div>
                <button class="btn btn-primary btn-small" onclick="copyLink()">
                    📋 Копировать
                </button>
            </div>

            <!-- Карта -->
            <div class="map-container">
                <div class="link-title">Карта недвижимости</div>
                <div class="map-placeholder">
                    🗺️ Карта с вашими объектами
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="section-title">
                🏢 Моя недвижимость
                <div style="margin-left: auto;">
                    <button class="btn btn-primary" onclick="addProperty()">
                        ➕ Добавить недвижимость
                    </button>
                </div>
            </div>

            <div class="properties-grid" id="propertiesGrid">
                <!-- Карточки недвижимости будут добавляться здесь -->
            </div>

            <div class="empty-state" id="emptyState">
                <div class="empty-state-icon">🏠</div>
                <h3>У вас пока нет недвижимости</h3>
                <p>Добавьте свой первый объект, чтобы начать работать с клиентами</p>
                <button class="btn btn-primary" onclick="addProperty()">
                    ➕ Добавить недвижимость
                </button>
            </div>
        </div>
    </div>

    <!-- Модальное окно редактирования профиля -->
    <div class="modal" id="profileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Редактировать профиль</h3>
                <button class="close-btn" onclick="closeModal('profileModal')">&times;</button>
            </div>
            <form onsubmit="saveProfile(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">Имя</label>
                        <input type="text" id="firstName" value="Иван" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">Фамилия</label>
                        <input type="text" id="lastName" value="Петров" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="phone">Телефон</label>
                    <input type="tel" id="phone" value="+7 (999) 123-45-67" required>
                </div>
                <div class="form-group">
                    <label for="region">Регион</label>
                    <select id="region">
                        <option value="moscow" selected>Москва</option>
                        <option value="spb">Санкт-Петербург</option>
                        <option value="ekb">Екатеринбург</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Сохранить</button>
            </form>
        </div>
    </div>

    <!-- Модальное окно добавления недвижимости -->
    <div class="modal" id="propertyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Добавить недвижимость</h3>
                <button class="close-btn" onclick="closeModal('propertyModal')">&times;</button>
            </div>
            
            <div class="payment-notice">
                <h4>💳 Оплата размещения</h4>
                <p>За выставление точки на карте необходимо заплатить 500₽. 
                   Пока объект не оплачен, он будет показываться как "Ожидает оплаты".</p>
            </div>

            <form onsubmit="saveProperty(event)">
                <div class="form-group">
                    <label for="propertyTitle">Название *</label>
                    <input type="text" id="propertyTitle" required>
                </div>
                
                <div class="form-group">
                    <label for="propertyDescription">Описание</label>
                    <textarea id="propertyDescription" rows="4"></textarea>
                </div>

                <div class="form-group">
                    <label for="propertyAddress">Адрес или координаты *</label>
                    <input type="text" id="propertyAddress" placeholder="Москва, ул. Тверская, 1 или 55.755814, 37.617635" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="propertyPrice">Цена *</label>
                        <input type="number" id="propertyPrice" required>
                    </div>
                    <div class="form-group">
                        <label for="dealType">Тип сделки *</label>
                        <select id="dealType" required>
                            <option value="">Выберите тип</option>
                            <option value="sale">Продажа</option>
                            <option value="rent">Аренда</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="propertyType">Тип недвижимости *</label>
                    <select id="propertyType" required>
                        <option value="">Выберите тип</option>
                        <option value="1+1">1+1 (евро)</option>
                        <option value="2+1">2+1 (две спальни и зал)</option>
                        <option value="3+1">3+1 (три спальни и зал)</option>
                        <option value="studio">Студия</option>
                        <option value="house">Дом</option>
                        <option value="commercial">Коммерческая</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="amenities">Блага цивилизации</label>
                    <textarea id="amenities" rows="3" placeholder="Лифт, парковка, охрана, детская площадка..."></textarea>
                </div>

                <div class="form-group">
                    <label>Данные владельца</label>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="ownerPhone">Телефон владельца</label>
                            <input type="tel" id="ownerPhone">
                        </div>
                        <div class="form-group">
                            <label for="ownerEmail">Email владельца</label>
                            <input type="email" id="ownerEmail">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="ownerSocial">Социальные сети владельца</label>
                    <input type="text" id="ownerSocial" placeholder="@username, ссылка на профиль">
                </div>

                <div class="form-group">
                    <label>Изображения</label>
                    <div class="file-upload" onclick="document.getElementById('propertyImages').click()">
                        <p>📸 Нажмите для выбора изображений</p>
                        <p style="font-size: 0.9rem; color: #666;">Поддерживаются: JPG, PNG, GIF</p>
                        <input type="file" id="propertyImages" multiple accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="telegramImport">Импорт по Telegram</label>
                    <input type="text" id="telegramImport" placeholder="Ссылка на сообщение или канал">
                </div>

                <button type="submit" class="btn btn-primary">Сохранить и оплатить</button>
            </form>
        </div>
    </div>

    <!-- Модальное окно календаря -->
    <div class="modal" id="calendarModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Календарь занятости</h3>
                <button class="close-btn" onclick="closeModal('calendarModal')">&times;</button>
            </div>
            
            <div class="calendar-container">
                <div class="calendar-header">
                    <button class="btn btn-outline btn-small" onclick="prevMonth()">◀</button>
                    <h4 id="currentMonth">Декабрь 2024</h4>
                    <button class="btn btn-outline btn-small" onclick="nextMonth()">▶</button>
                </div>
                
                <div class="calendar-grid" id="calendarGrid">
                    <!-- Календарь будет генерироваться здесь -->
                </div>
                
                <div style="margin-top: 1rem;">
                    <p style="font-size: 0.9rem; color: #666;">
                        <span style="color: #4caf50;">●</span> Доступно
                        <span style="color: #d32f2f; margin-left: 1rem;">●</span> Забронировано
                        <span style="color: #ccc; margin-left: 1rem;">●</span> Недоступно
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Данные недвижимости (в реальном приложении приходят с сервера)
        let properties = [
            {
                id: 1,
                title: "Квартира в центре",
                price: 5000000,
                type: "2+1",
                address: "Москва, ул. Тверская, 10",
                dealType: "sale",
                paid: true
            },
            {
                id: 2,
                title: "Студия у метро",
                price: 50000,
                type: "studio",
                address: "Москва, ул. Арбат, 25",
                dealType: "rent",
                paid: false
            }
        ];

        // Инициализация страницы
        document.addEventListener('DOMContentLoaded', function() {
            renderProperties();
            setupFileUpload();
        });

        function renderProperties() {
            const grid = document.getElementById('propertiesGrid');
            const emptyState = document.getElementById('emptyState');
            
            if (properties.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            grid.style.display = 'grid';
            emptyState.style.display = 'none';
            
            grid.innerHTML = properties.map(property => `
                <div class="property-card">
                    <div class="property-title">${property.title}</div>
                    <div class="property-price">${property.price.toLocaleString()} ${property.dealType === 'sale' ? '₽' : '₽/мес'}</div>
                    <div class="property-type">${property.type}</div>
                    <div class="property-address">📍 ${property.address}</div>
                    ${!property.paid ? '<div style="color: #f44336; font-size: 0.8rem;">⏳ Ожидает оплаты</div>' : ''}
                    <div class="property-actions">
                        <button class="btn btn-outline btn-small" onclick="editProperty(${property.id})">✏️ Редактировать</button>
                        <button class="btn btn-primary btn-small" onclick="showCalendar(${property.id})">📅 Календарь</button>
                        <button class="btn btn-danger btn-small" onclick="deleteProperty(${property.id})">🗑️ Удалить</button>
                    </div>
                </div>
            `).join('');
        }

        function editProfile() {
            document.getElementById('profileModal').style.display = 'block';
        }

        function addProperty() {
            document.getElementById('propertyModal').style.display = 'block';
        }

        function editProperty(id) {
            const property = properties.find(p => p.id === id);
            if (property) {
                // Заполняем форму данными недвижимости
                document.getElementById('propertyTitle').value = property.title;
                document.getElementById('propertyAddress').value = property.address;
                document.getElementById('propertyPrice').value = property.price;
                document.getElementById('dealType').value = property.dealType;
                document.getElementById('propertyType').value = property.type;
                
                document.getElementById('propertyModal').style.display = 'block';
            }
        }

        function deleteProperty(id) {
            if (confirm('Вы уверены, что хотите удалить эту недвижимость?')) {
                properties = properties.filter(p => p.id !== id);
                renderProperties();
            }
        }

        function showCalendar(id) {
            document.getElementById('calendarModal').style.display = 'block';
            generateCalendar();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function saveProfile(event) {
            event.preventDefault();
            // Здесь будет логика сохранения профиля
            closeModal('profileModal');
        }

        function saveProperty(event) {
            event.preventDefault();
            // Здесь будет логика сохранения недвижимости
            closeModal('propertyModal');
        }

        function setupFileUpload() {
            const fileInput = document.getElementById('propertyImages');
            const fileUpload = document.querySelector('.file-upload');
            fileInput.addEventListener('change', function() {
                fileUpload.textContent = fileInput.files.length + ' файлов выбрано';
            });
        }

        // Календарь
        function generateCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            calendarGrid.innerHTML = '';
            
            const daysInMonth = 31; // Для упрощения, считаем, что всегда 31 день
            for (let i = 1; i <= daysInMonth; i++) {
                const dayCell = document.createElement('div');
                dayCell.classList.add('day-cell');
                dayCell.textContent = i;
                calendarGrid.appendChild(dayCell);
            }
        }

        function prevMonth() {
            // Здесь будет логика для предыдущего месяца
        }

        function nextMonth() {
            // Здесь будет логика для следующего месяца
        }
    </script>
</body>
</html>
