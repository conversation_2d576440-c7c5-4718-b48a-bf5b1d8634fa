<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Панель глобального администратора</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2e7d32;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #4caf50;
            font-size: 1.2em;
        }

        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
        }

        .tab {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #2e7d32;
            border-bottom-color: #4caf50;
            font-weight: 600;
        }

        .tab:hover {
            color: #2e7d32;
            background: rgba(76, 175, 80, 0.1);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8f5e8;
        }

        .section h2 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .btn {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }

        .btn-danger:hover {
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 0.9em;
        }

        .admins-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .admins-table th,
        .admins-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .admins-table th {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            font-weight: 600;
        }

        .admins-table tr:hover {
            background: rgba(76, 175, 80, 0.05);
            cursor: pointer;
        }

        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #4caf50;
        }

        .contact-icons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .contact-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .contact-icon:hover {
            transform: scale(1.1);
        }

        .contact-icon.email { background: #4caf50; }
        .contact-icon.phone { background: #2196f3; }
        .contact-icon.telegram { background: #0088cc; }
        .contact-icon.whatsapp { background: #25d366; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.1em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid #4caf50;
        }

        .stat-card h3 {
            color: #2e7d32;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #4caf50;
            font-weight: 600;
        }

        .proposals-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .proposal-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 4px solid #4caf50;
        }

        .proposal-item h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .proposal-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .claims-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .claim-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 4px solid #ff9800;
        }

        .claim-item h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .claim-author {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .claim-author img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }

        .response-form {
            display: none;
            margin-top: 15px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .response-form textarea {
            width: 100%;
            height: 100px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            resize: vertical;
            font-family: inherit;
        }

        .response-form textarea:focus {
            outline: none;
            border-color: #4caf50;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4caf50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .status-active {
            background: #c8e6c9;
            color: #2e7d32;
        }

        .status-blocked {
            background: #ffcdd2;
            color: #d32f2f;
        }

        .refresh-btn {
            float: right;
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #2e7d32;
            font-weight: 600;
        }

        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #4caf50;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .response-time-chart {
            width: 100%;
            height: 250px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1em;
        }

        .total-revenue {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }

        .total-revenue h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .total-revenue p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .proposal-info {
            margin-bottom: 10px;
        }

        .proposal-timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .claim-content {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .claim-timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            opacity: 0;
            transform: translateX(300px);
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            background: #4caf50;
        }

        .notification.error {
            background: #f44336;
        }

        .blocked-admin {
            opacity: 0.7;
        }

        .blocked-admin .admins-table td {
            background: #ffebee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Панель глобального администратора</h1>
            <p>Управление региональными администраторами</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('admins')">Администраторы</button>
            <button class="tab" onclick="showTab('proposals')">Заявки на регистрацию</button>
            <button class="tab" onclick="showTab('claims')">Вопросы от администраторов</button>
        </div>

        <!-- Вкладка "Администраторы" -->
        <div id="admins" class="tab-content active">
            <div class="section">
                <h2>Список региональных администраторов</h2>
                <button class="btn refresh-btn" onclick="loadAdmins()">Обновить данные</button>
                
                <div class="total-revenue">
                    <h3 id="totalRevenue">0₽</h3>
                    <p>Общий доход до вычета налогов</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="totalAdmins">0</h3>
                        <p>Всего администраторов</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="activeAdmins">0</h3>
                        <p>Активных администраторов</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalApplications">0</h3>
                        <p>Всего заявок</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="processedApplications">0</h3>
                        <p>Обработанных заявок</p>
                    </div>
                </div>

                <div id="adminsLoading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Загрузка данных...</p>
                </div>

                <table class="admins-table" id="adminsTable">
                    <thead>
                        <tr>
                            <th>Администратор</th>
                            <th>Контакты</th>
                            <th>Заявки</th>
                            <th>Обработано</th>
                            <th>Доход</th>
                            <th>Статус</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody id="adminsTableBody">
                        <!-- Данные будут загружены через JavaScript -->
                    </tbody>
                </table>

                <div class="charts-grid">
                    <div class="chart-container">
                        <div>
                            <h3>График обработанных заявок</h3>
                            <p>Статистика по администраторам за месяц</p>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div>
                            <h3>График решенных вопросов</h3>
                            <p>Количество решенных вопросов за месяц</p>
                        </div>
                    </div>
                </div>

                <div class="response-time-chart">
                    <div>
                        <h3>Время ответа администраторов</h3>
                        <p>Среднее время ответа на сообщения</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Вкладка "Заявки на регистрацию" -->
        <div id="proposals" class="tab-content">
            <div class="section">
                <h2>Заявки на регистрацию администраторов</h2>
                <button class="btn refresh-btn" onclick="loadProposals()">Обновить заявки</button>
                
                <div id="proposalsLoading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Загрузка заявок...</p>
                </div>

                <div class="proposals-list" id="proposalsList">
                    <!-- Заявки будут загружены через JavaScript -->
                </div>
            </div>
        </div>

        <!-- Вкладка "Вопросы от администраторов" -->
        <div id="claims" class="tab-content">
            <div class="section">
                <h2>Вопросы от региональных администраторов</h2>
                <button class="btn refresh-btn" onclick="loadClaims()">Обновить вопросы</button>
                
                <div id="claimsLoading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Загрузка вопросов...</p>
                </div>

                <div class="claims-list" id="claimsList">
                    <!-- Вопросы будут загружены через JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Модальное окно для детальной информации об администраторе -->
    <div id="adminModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('adminModal')">&times;</span>
            <div id="adminDetails">
                <!-- Детали администратора будут загружены через JavaScript -->
            </div>
        </div>
    </div>

    <!-- Модальное окно для причины отказа -->
    <div id="refusalModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('refusalModal')">&times;</span>
            <h2>Причина отказа</h2>
            <div class="input-group">
                <label for="refusalReason">Укажите причину отказа:</label>
                <textarea id="refusalReason" rows="5" placeholder="Введите причину отказа..."></textarea>
            </div>
            <button class="btn" onclick="sendRefusal()">Отправить отказ</button>
        </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification"></div>

    <script>
        // Глобальные переменные
        let currentRegion = 'region-uuid-123'; // ID региона
        let currentProposalId = null;
        let currentClaimId = null;
        let mockAdmins = [];
        let mockProposals = [];
        let mockClaims = [];

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initializeMockData();
            loadAdmins();
            loadProposals();
            loadClaims();
        });

        // Переключение вкладок
        function showTab(tabName) {
            // Скрыть все вкладки
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(button => button.classList.remove('active'));
            
            // Показать выбранную вкладку
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Инициализация демо-данных
        function initializeMockData() {
            mockAdmins = [
                {
                    id: 'admin-1',
                    firstName: 'Анна',
                    lastName: 'Петрова',
                    email: '<EMAIL>',
                    phone: '******-111-22-33',
                    telegram: '@anna_petrova',
                    whatsapp: '******-111-22-33',
                    preferredContact: 'email',
                    avatar: 'https://via.placeholder.com/50/4CAF50/FFFFFF?text=АП',
                    totalApplications: 45,
                    processedApplications: 42,
                    revenue: 150000,
                    status: 'active',
                    avgResponseTime: '2.5 часа',
                    solvedQuestions: 23
                },
                {
                    id: 'admin-2',
                    firstName: 'Михаил',
                    lastName: 'Сидоров',
                    email: '<EMAIL>',
                    phone: '******-222-33-44',
                    telegram: '@mikhail_sidorov',
                    whatsapp: '******-222-33-44',
                    preferredContact: 'telegram',
                    avatar: 'https://via.placeholder.com/50/4CAF50/FFFFFF?text=МС',
                    totalApplications: 38,
                    processedApplications: 35,
                    revenue: 120000,
                    status: 'active',
                    avgResponseTime: '1.8 часа',
                    solvedQuestions: 19
                },
                {
                    id: 'admin-3',
                    firstName: 'Елена',
                    lastName: 'Козлова',
                    email: '<EMAIL>',
                    phone: '******-333-44-55',
                    telegram: '@elena_kozlova',
                    whatsapp: '******-333-44-55',
                    preferredContact: 'phone',
                    avatar: 'https://via.placeholder.com/50/4CAF50/FFFFFF?text=ЕК',
                    totalApplications: 28,
                    processedApplications: 20,
                    revenue: 80000,
                    status: 'blocked',
                    avgResponseTime: '4.2 часа',
                    solvedQuestions: 12
                }
            ];

            mockProposals = [
                {
                    id: 'admin-proposal-1',
                    firstName: 'Владимир',
                    lastName: 'Николаев',
                    email: '<EMAIL>',
                    phone: '******-444-55-66',
                    telegram: '@vladimir_nikolaev',
                    whatsapp: '******-444-55-66',
                    preferredContact: 'email',
                    submittedAt: '2025-07-06T09:30:00Z',
                    experience: '5 лет опыта в управлении персоналом'
                },
                {
                    id: 'admin-proposal-2',
                    firstName: 'Ольга',
                    lastName: 'Смирнова',
                    email: '<EMAIL>',
                    phone: '******-555-66-77',
                    telegram: '@olga_smirnova',
                    whatsapp: '******-555-66-77',
                    preferredContact: 'phone',
                    submittedAt: '2025-07-05T15:45:00Z',
                    experience: '3 года работы в сфере недвижимости'
                }
            ];

            mockClaims = [
                {
                    id: 'admin-claim-1',
                    title: 'Проблема с доступом к системе отчетности',
                    content: 'Не могу получить доступ к разделу аналитики. Выдает ошибку "Доступ запрещен".',
                    author: {
                        firstName: 'Анна',
                        lastName: 'Петрова',
                        avatar: 'https://via.placeholder.com/30/4CAF50/FFFFFF?text=АП'
                    },
                    submittedAt: '2025-07-06T11:20:00Z',
                    status: 'open'
                },
                {
                    id: 'admin-claim-2',
                    title: 'Вопрос по процедуре блокировки агентов',
                    content: 'Подскажите, какие документы нужны для блокировки агента за нарушение правил?',
                    author: {
                        firstName: 'Михаил',
                        lastName: 'Сидоров',
                        avatar: 'https://via.placeholder.com/30/4CAF50/FFFFFF?text=МС'
                    },
                    submittedAt: '2025-07-05T14:10:00Z',
                    status: 'open'
                }
            ];
        }

        // Загрузка списка администраторов
        function loadAdmins() {
            const loading = document.getElementById('adminsLoading');
            const tableBody = document.getElementById('adminsTableBody');
            
            loading.style.display = 'block';
            tableBody.innerHTML = '';

            // Симуляция API запроса GET path/admins?region=uuid_region
            setTimeout(() => {
                loading.style.display = 'none';
                
                // Обновление статистики
                updateAdminStats();
                
                // Заполнение таблицы
                mockAdmins.forEach(admin => {
                    const row = document.createElement('tr');
                    if (admin.status === 'blocked') {
                        row.classList.add('blocked-admin');
                    }
                    row.innerHTML = `
                        <td>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <img src="${admin.avatar}" alt="${admin.firstName}" class="avatar">
                                <div>
                                    <strong>${admin.firstName} ${admin.lastName}</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>Предпочтительный: ${getContactTypeText(admin.preferredContact)}</div>
                            <div class="contact-icons">
                                <div class="contact-icon email" title="Email: ${admin.email}">@</div>
                                <div class="contact-icon phone" title="Телефон: ${admin.phone}">📞</div>
                                <div class="contact-icon telegram" title="Telegram: ${admin.telegram}">T</div>
                                <div class="contact-icon whatsapp" title="WhatsApp: ${admin.whatsapp}">W</div>
                            </div>
                        </td>
                        <td><strong>${admin.totalApplications}</strong></td>
                        <td><strong>${admin.processedApplications}</strong></td>
                        <td><strong>${admin.revenue.toLocaleString()}₽</strong></td>
                        <td>
                            <span class="status-badge ${admin.status === 'active' ? 'status-active' : 'status-blocked'}">
                                ${admin.status === 'active' ? 'Активен' : 'Заблокирован'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-small" onclick="showAdminDetails('${admin.id}')">Подробнее</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }, 1000);
        }

        // Обновление статистики администраторов
        function updateAdminStats() {
            const totalAdmins = mockAdmins.length;
            const activeAdmins = mockAdmins.concat;
            document.getElementById('totalAdmins').textContent = totalAdmins;
            document.getElementById('activeAdmins').textContent = activeAdmins;
            document.getElementById('avgResponseTime').textContent = '2.3'; // Среднее время ответа
        }

        // Загрузка заявок на регистрацию
        function loadProposals() {
            const loading = document.getElementById('proposalsLoading');
            const proposalsList = document.getElementById('proposalsList');
            
            loading.style.display = 'block';
            proposalsList.innerHTML = '';

            // Симуляция API запроса GET path/admins/proposals?region=uuid_region
            setTimeout(() => {
                loading.style.display = 'none';
                
                mockProposals.forEach(proposal => {
                    const item = document.createElement('div');
                    item.className = 'list-item';
                    item.innerHTML = `
                        <div class="item-info">
                            <strong>${proposal.firstName} ${proposal.lastName}</strong><br>
                            <small>Подана: ${new Date(proposal.submittedAt).toLocaleString()}</small><br>
                            <small>Опыт: ${proposal.experience}</small>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-success" onclick="approveProposal('${proposal.id}')">Добавить</button>
                            <button class="btn btn-danger" onclick="rejectProposal('${proposal.id}')">Отказать</button>
                        </div>
                    `;
                    proposalsList.appendChild(item);
                });
            }, 1000);
        }

        // Загрузка вопросов от администраторов
        function loadClaims() {
            const loading = document.getElementById('claimsLoading');
            const claimsList = document.getElementById('claimsList');
            
            loading.style.display = 'block';
            claimsList.innerHTML = '';

            // Симуляция API запроса GET path/admins/claims?region=uuid_region
            setTimeout(() => {
                loading.style.display = 'none';
                
                mockClaims.forEach(claim => {
                    const item = document.createElement('div');
                    item.className = 'list-item';
                    item.innerHTML = `
                        <div class="item-info">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <img src="${claim.author.avatar}" alt="Аватар" class="avatar" style="width: 30px; height: 30px;">
                                <div>
                                    <strong>${claim.title}</strong><br>
                                    <small>Автор: ${claim.author.firstName} ${claim.author.lastName} | ${new Date(claim.submittedAt).toLocaleString()}</small>
                                </div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <button class="btn" onclick="answerClaim('${claim.id}')">Ответить</button>
                        </div>
                    `;
                    claimsList.appendChild(item);
                });
            }, 1000);
        }

        // Отображение подробностей администратора
        function showAdminDetails(adminId) {
            const admin = mockAdmins.find(a => a.id === adminId);
            if (admin) {
                document.getElementById('adminDetailsModal').style.display = 'block';
                document.getElementById('adminName').textContent = `${admin.firstName} ${admin.lastName}`;
                document.getElementById('adminAvatar').src = admin.avatar;
                document.getElementById('adminPreferredContact').textContent = `Предпочтительный контакт: ${getContactTypeText(admin.preferredContact)}`;
                document.getElementById('adminProcessed').textContent = admin.processedApplications;
                document.getElementById('adminTotal').textContent = admin.totalApplications;
                document.getElementById('adminRevenue').textContent = admin.revenue.toLocaleString() + '₽';
            }
        }

        // Закрытие модального окна
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Инициализация страницы
        document.addEventListener('DOMContentLoaded', function() {
            loadAdmins();
            loadProposals();
            loadClaims();
        });
    </script>
</body>
</html>