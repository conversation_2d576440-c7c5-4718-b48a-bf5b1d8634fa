<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RealEstate CRM - Главная</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a4a14 0%, #2d5a27 25%, #4a7c59 100%);
            color: #fff;
            overflow-x: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header.scrolled {
            background: rgba(0, 0, 0, 0.4);
            padding: 0.5rem 2rem;
        }

        .logo {
            color: #e8f5e8;
            font-size: 1.8rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #a8d5a8;
            color: #a8d5a8;
        }

        .btn-outline:hover {
            background: #a8d5a8;
            color: #2d5a27;
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #66bb6a, #4caf50);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 187, 106, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 187, 106, 0.4);
        }

        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            padding: 0 2rem;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="grad1" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" /><stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" /></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23grad1)" opacity="0.3"/><circle cx="800" cy="300" r="150" fill="url(%23grad1)" opacity="0.2"/><circle cx="400" cy="700" r="120" fill="url(%23grad1)" opacity="0.25"/></svg>') center/cover;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
            position: relative;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #e8f5e8, #a8d5a8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titlePulse 3s ease-in-out infinite;
        }

        @keyframes titlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 15px;
            min-width: 180px;
        }

        .features {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #e8f5e8;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #e8f5e8;
        }

        .feature-description {
            opacity: 0.9;
            line-height: 1.6;
        }

        .roles-section {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.05) 100%);
        }

        .roles-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .role-card {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(102, 187, 106, 0.1));
            padding: 2.5rem;
            border-radius: 20px;
            border: 2px solid rgba(76, 175, 80, 0.3);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: transform 0.6s;
            transform: rotate(45deg) translateX(-100%);
        }

        .role-card:hover::before {
            transform: rotate(45deg) translateX(100%);
        }

        .role-card:hover {
            transform: translateY(-5px);
            border-color: rgba(76, 175, 80, 0.6);
            box-shadow: 0 15px 30px rgba(76, 175, 80, 0.2);
        }

        .role-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }

        .role-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #e8f5e8;
        }

        .role-description {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .cta-section {
            padding: 6rem 2rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(45, 90, 39, 0.2) 100%);
        }

        .cta-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2.2rem;
            margin-bottom: 1rem;
            color: #e8f5e8;
        }

        .cta-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin: 3rem 0;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4caf50;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }

        .footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 3rem 2rem 1rem;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #a8d5a8;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: #e8f5e8;
        }

        .footer-text {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .roles-grid {
                grid-template-columns: 1fr;
            }

            .stats {
                gap: 2rem;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header" id="header">
        <div class="logo">
            🏠 RealEstate CRM
        </div>
        <nav class="nav-buttons">
            <a href="#features" class="btn btn-outline">Возможности</a>
            <a href="#roles" class="btn btn-outline">Роли</a>
            <a href="#" class="btn btn-primary" onclick="showLogin()">Войти</a>
        </nav>
    </header>

    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Управляйте недвижимостью профессионально</h1>
            <p class="hero-subtitle">
                Современная CRM система для риелторов и региональных администраторов. 
                Упростите работу с клиентами, объектами и командой.
            </p>
            <div class="hero-buttons">
                <a href="#" class="btn btn-primary hero-btn" onclick="showRegister()">Регистрация</a>
                <a href="#" class="btn btn-secondary hero-btn" onclick="showLogin()">Войти в систему</a>
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="features-container">
            <h2 class="section-title">Возможности системы</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">📊</span>
                    <h3 class="feature-title">Управление клиентами</h3>
                    <p class="feature-description">
                        Централизованная база клиентов с историей сделок, предпочтениями и статусами.
                    </p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🏢</span>
                    <h3 class="feature-title">Каталог объектов</h3>
                    <p class="feature-description">
                        Удобное управление объектами недвижимости с фотографиями, характеристиками и ценами.
                    </p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">💼</span>
                    <h3 class="feature-title">Управление сделками</h3>
                    <p class="feature-description">
                        Отслеживание всех этапов сделки от первого контакта до подписания договора.
                    </p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📈</span>
                    <h3 class="feature-title">Аналитика и отчеты</h3>
                    <p class="feature-description">
                        Подробная статистика по продажам, конверсии и эффективности работы.
                    </p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🔗</span>
                    <h3 class="feature-title">Реферальная система</h3>
                    <p class="feature-description">
                        Привлекайте новых риелторов и получайте бонусы за каждого активного участника.
                    </p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <h3 class="feature-title">Мультиканальность</h3>
                    <p class="feature-description">
                        Работайте через телефон, Telegram, email - выбирайте удобный канал связи.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <section class="roles-section" id="roles">
        <div class="roles-container">
            <h2 class="section-title">Роли в системе</h2>
            <div class="roles-grid">
                <div class="role-card">
                    <span class="role-icon">🏘️</span>
                    <h3 class="role-title">Риелтор</h3>
                    <p class="role-description">
                        Работайте с клиентами, ведите сделки, управляйте объектами. 
                        Получайте доступ к персональной панели с аналитикой.
                    </p>
                    <a href="#" class="btn btn-primary" onclick="showRegister('realtor')">Стать риелтором</a>
                </div>
                <div class="role-card">
                    <span class="role-icon">🎯</span>
                    <h3 class="role-title">Региональный администратор</h3>
                    <p class="role-description">
                        Управляйте командой риелторов в вашем регионе. 
                        Контролируйте процессы и развивайте бизнес.
                    </p>
                    <a href="#" class="btn btn-secondary" onclick="showRegister('admin')">Стать администратором</a>
                </div>
                <div class="role-card">
                    <span class="role-icon">⚡</span>
                    <h3 class="role-title">Глобальный администратор</h3>
                    <p class="role-description">
                        Полный контроль над системой, управление всеми регионами 
                        и стратегическое планирование.
                    </p>
                    <a href="#" class="btn btn-outline" onclick="showLogin()">Войти в систему</a>
                </div>
            </div>
        </div>
    </section>

    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title">Начните работу уже сегодня</h2>
            <p class="cta-subtitle">
                Присоединяйтесь к тысячам профессионалов, которые уже используют нашу систему 
                для эффективного управления недвижимостью.
            </p>
            
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">2,500+</span>
                    <span class="stat-label">Активных риелторов</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">150+</span>
                    <span class="stat-label">Городов</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50,000+</span>
                    <span class="stat-label">Сделок в месяц</span>
                </div>
            </div>

            <div class="hero-buttons">
                <a href="#" class="btn btn-primary hero-btn" onclick="showRegister()">Начать сейчас</a>
                <a href="#" class="btn btn-outline hero-btn" onclick="showDemo()">Демо-версия</a>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#" class="footer-link">О нас</a>
                <a href="#" class="footer-link">Условия использования</a>
                <a href="#" class="footer-link">Политика конфиденциальности</a>
                <a href="#" class="footer-link">Поддержка</a>
                <a href="#" class="footer-link">Контакты</a>
            </div>
            <p class="footer-text">© 2025 RealEstate CRM. Все права защищены.</p>
        </div>
    </footer>

    <script>
        // Анимация хедера при скролле
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Плавный скролл для якорных ссылок
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Функции для навигации
        function showLogin() {
            // В реальном приложении здесь был бы редирект на /login
            alert('Переход на страницу входа (/login)');
        }

        function showRegister(role = '') {
            // В реальном приложении здесь был бы редирект на /register
            if (role) {
                alert(`Переход на страницу регистрации (/register) с предвыбранной ролью: ${role}`);
            } else {
                alert('Переход на страницу регистрации (/register)');
            }
        }

        function showDemo() {
            alert('Демо-версия будет доступна в ближайшее время!');
        }

        // Анимация появления элементов при скролле
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Применяем анимацию к карточкам
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .role-card');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>